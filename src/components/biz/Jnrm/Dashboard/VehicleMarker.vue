<script setup lang="ts">
import { type TransportOrder, TransportOrderStatus } from '~/stores/useTransportOrderStore'

interface Props {
	order: TransportOrder
}

const props = defineProps<Props>()

// 使用运单store
const orderStore = useTransportOrderStore()

/**
 * 创建精致的煤炭运输卡车图标，带状态覆盖层
 */
const createCoalTruckIcon = (order: TransportOrder) => {
	// 获取运单状态颜色
	const statusColor = orderStore.getOrderStatusColor(order)
	
	// 根据在线状态确定覆盖层样式
	const isOnline = order.vehicleOnlineStatus === 'online'
	
	// 创建精致的煤炭运输卡车SVG图标
	const svgIcon = `
		<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
			<defs>
				<!-- 渐变定义 -->
				<linearGradient id="truckBodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
					<stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
					<stop offset="50%" style="stop-color:#374151;stop-opacity:1" />
					<stop offset="100%" style="stop-color:#111827;stop-opacity:1" />
				</linearGradient>
				<linearGradient id="cabGradient" x1="0%" y1="0%" x2="100%" y2="100%">
					<stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
					<stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
				</linearGradient>
				<linearGradient id="coalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
					<stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
					<stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
				</linearGradient>
				<!-- 阴影滤镜 -->
				<filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
					<feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="rgba(0,0,0,0.4)"/>
				</filter>
			</defs>
			
			<!-- 车辆阴影 -->
			<ellipse cx="32" cy="58" rx="28" ry="4" fill="rgba(0,0,0,0.2)"/>
			
			<!-- 卡车主体 -->
			<g transform="translate(6, 14)" filter="url(#shadow)">
				<!-- 驾驶室 -->
				<rect x="2" y="16" width="16" height="20" rx="3" fill="url(#cabGradient)" stroke="#1e40af" stroke-width="1.5"/>
				
				<!-- 驾驶室顶部 -->
				<path d="M 4 16 L 4 8 Q 4 6 6 6 L 14 6 Q 16 6 16 8 L 16 16 Z" fill="#60a5fa" stroke="#2563eb" stroke-width="1"/>
				
				<!-- 车窗 -->
				<rect x="5" y="8" width="10" height="6" rx="2" fill="#e0f2fe" stroke="#81d4fa" stroke-width="0.5" opacity="0.9"/>
				<line x1="10" y1="8" x2="10" y2="14" stroke="#81d4fa" stroke-width="0.5" opacity="0.7"/>
				
				<!-- 货厢底盘 -->
				<rect x="18" y="18" width="32" height="18" rx="2" fill="url(#truckBodyGradient)" stroke="#111827" stroke-width="1.5"/>
				
				<!-- 煤炭货物 -->
				<rect x="20" y="12" width="28" height="8" rx="1" fill="url(#coalGradient)" stroke="#000000" stroke-width="1"/>
				<!-- 煤炭颗粒效果 -->
				<circle cx="24" cy="15" r="1" fill="#374151"/>
				<circle cx="28" cy="14" r="1.2" fill="#1f2937"/>
				<circle cx="32" cy="16" r="0.8" fill="#374151"/>
				<circle cx="36" cy="15" r="1.1" fill="#1f2937"/>
				<circle cx="40" cy="14" r="0.9" fill="#374151"/>
				<circle cx="44" cy="16" r="1" fill="#1f2937"/>
				
				<!-- 车轮 -->
				<circle cx="12" cy="38" r="6" fill="#1f2937" stroke="#000000" stroke-width="1.5"/>
				<circle cx="26" cy="38" r="6" fill="#1f2937" stroke="#000000" stroke-width="1.5"/>
				<circle cx="42" cy="38" r="6" fill="#1f2937" stroke="#000000" stroke-width="1.5"/>
				<!-- 轮毂 -->
				<circle cx="12" cy="38" r="3.5" fill="#374151"/>
				<circle cx="26" cy="38" r="3.5" fill="#374151"/>
				<circle cx="42" cy="38" r="3.5" fill="#374151"/>
				<circle cx="12" cy="38" r="2" fill="#6b7280"/>
				<circle cx="26" cy="38" r="2" fill="#6b7280"/>
				<circle cx="42" cy="38" r="2" fill="#6b7280"/>
				
				<!-- 车灯 -->
				<rect x="50" y="22" width="3" height="4" rx="1.5" fill="#fbbf24"/>
				<rect x="50" y="28" width="3" height="4" rx="1.5" fill="#ef4444"/>
				
				<!-- 车门 -->
				<rect x="16" y="20" width="1" height="12" rx="0.5" fill="#1e40af"/>
				<circle cx="15" cy="24" r="0.8" fill="#93c5fd"/>
				
				<!-- 装饰线条和细节 -->
				<line x1="20" y1="24" x2="48" y2="24" stroke="#000000" stroke-width="1" opacity="0.5"/>
				<line x1="20" y1="30" x2="48" y2="30" stroke="#000000" stroke-width="1" opacity="0.5"/>
				<rect x="22" y="32" width="24" height="2" fill="#1f2937"/>
			</g>
			
			<!-- 状态覆盖层 - 覆盖整个卡车 -->
			<rect x="4" y="12" width="56" height="40" rx="28"
				  fill="${statusColor}"
				  opacity="${isOnline ? '0.2' : '0.5'}"
				  stroke="${statusColor}"
				  stroke-width="3"
				  stroke-dasharray="${isOnline ? '0' : '8,4'}">
				${!isOnline ? `
					<animate attributeName="opacity" values="0.5;0.8;0.5" dur="2s" repeatCount="indefinite"/>
				` : ''}
			</rect>
			
			<!-- 在线状态指示器 -->
			<circle cx="52" cy="12" r="6" fill="${isOnline ? '#10b981' : '#ef4444'}" stroke="#ffffff" stroke-width="2">
				${isOnline ? `
					<animate attributeName="r" values="6;8;6" dur="3s" repeatCount="indefinite"/>
					<animate attributeName="opacity" values="1;0.7;1" dur="3s" repeatCount="indefinite"/>
				` : `
					<animate attributeName="fill" values="#ef4444;#dc2626;#ef4444" dur="1s" repeatCount="indefinite"/>
				`}
			</circle>
			${isOnline ?
				`<circle cx="52" cy="12" r="3" fill="#ffffff">
					<animate attributeName="opacity" values="1;0.5;1" dur="3s" repeatCount="indefinite"/>
				</circle>` :
				`<text x="52" y="16" text-anchor="middle" fill="#ffffff" font-size="10" font-weight="bold">!</text>`
			}
		</svg>
	`
	
	// 将SVG转换为Data URL
	const svgDataUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgIcon)))
	
	return new BMapGL.Icon(svgDataUrl, new BMapGL.Size(64, 64), {
		anchor: new BMapGL.Size(32, 32),
	})
}

/**
 * 创建车辆标签 - 只显示车牌号和状态
 */
const createVehicleLabel = (order: TransportOrder) => {
	const isOnline = order.vehicleOnlineStatus === 'online'
	const statusText = isOnline ? '在线' : '离线'
	const statusColor = orderStore.getOrderStatusColor(order)
	
	return new BMapGL.Label(
		`<div style="text-align: center; min-width: 100px;">
			<div style="font-size: 12px; font-weight: bold; color: white; margin-bottom: 1px;">
				${order.vehicle.licensePlate || '--'}
			</div>
			<div style="display: flex; align-items: center; justify-content: center; gap: 3px;">
				<div style="width: 5px; height: 5px; border-radius: 50%; background-color: ${statusColor};"></div>
				<span style="font-size: 9px; color: ${statusColor}; font-weight: 500;">${statusText}</span>
			</div>
		</div>`,
		{
			offset: new BMapGL.Size(-50, -85),
		},
	)
}

/**
 * 设置标签样式
 */
const setLabelStyle = (label: any, order: TransportOrder) => {
	const statusColor = orderStore.getOrderStatusColor(order)
	
	label.setStyle({
		color: 'white',
		fontSize: '9px',
		background: 'rgba(0, 0, 0, 0.8)',
		border: `1px solid ${statusColor}`,
		borderRadius: '6px',
		padding: '4px 6px',
		whiteSpace: 'nowrap',
		boxShadow: '0 2px 8px rgba(0, 0, 0, 0.4)',
	})
}

// 导出组件方法供地图使用
defineExpose({
	createCoalTruckIcon,
	createVehicleLabel,
	setLabelStyle,
})
</script>

<template>
	<!-- 这个组件主要用于提供车辆标记的创建方法，不需要模板 -->
</template>
